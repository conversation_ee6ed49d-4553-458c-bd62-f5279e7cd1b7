{"name": "server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.6.8", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.18.1", "multer": "^1.4.5-lts.1", "pedone-clob-client": "^1.0.1", "session-file-store": "^1.0.1", "tslib": "^2.6.2"}, "devDependencies": {"nodemon": "^3.1.7"}}