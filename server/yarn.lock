# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 30db19c78faa2b6ff27275ab767646929207bb207f903f09eb3e4c273ce2738b45f3c82169ddacd67468b4f063d8d96035f2bf36f02b6b7e4d928eefe2e3ecbc
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.0.6":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": ^4.0.1
    ethereum-cryptography: ^2.0.0
    micro-ftch: ^0.3.1
  checksum: 9ae5dee8f12b0faf81cd83f06a41560e79b0ba96a48262771d897a510ecae605eb6d84f687da001ab8ccffd50f612ae50f988ef76e6312c752897f462f3ac08d
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:5.8.0, @ethersproject/abi@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abi@npm:5.8.0"
  dependencies:
    "@ethersproject/address": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/hash": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/strings": ^5.8.0
  checksum: cdab990d520fdbfd63d4a8829e78a2d2d2cc110dc3461895bd9014a49d3a9028c2005a11e2569c3fd620cb7780dcb5c71402630a8082a9ca5f85d4f8700d4549
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:5.8.0, @ethersproject/abstract-provider@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abstract-provider@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/networks": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
    "@ethersproject/web": ^5.8.0
  checksum: 4fd00d770552af53be297c676f31d938f5dc44d73c24970036a11237a53f114cc1c551fd95937b9eca790f77087da1ed3ec54f97071df088d5861f575fd4f9be
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:5.8.0, @ethersproject/abstract-signer@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abstract-signer@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
  checksum: 3f7a98caf7c01e58da45d879c08449d1443bced36ac81938789c90d8f9ff86a1993655bae9805fc7b31a723b7bd7b4f1f768a9ec65dff032d0ebdc93133c14f3
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.8.0, @ethersproject/address@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/address@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/rlp": ^5.8.0
  checksum: fa48e16403b656207f996ee7796f0978a146682f10f345b75aa382caa4a70fbfdc6ff585e9955e4779f4f15a31628929b665d288b895cea5df206c070266aea1
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:5.8.0, @ethersproject/base64@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/base64@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
  checksum: f0c2136c99b2fd2f93b7e110958eacc5990e88274b1f38eb73d8eaa31bdead75fc0c4608dac23cb5718ae455b965de9dc5023446b96de62ef1fa945cbf212096
  languageName: node
  linkType: hard

"@ethersproject/basex@npm:5.8.0, @ethersproject/basex@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/basex@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/properties": ^5.8.0
  checksum: 7b502b91011d3aac9bf38d77aad113632440a1eab6a966ffbe2c23f9e3758a4dcb2a4189ab2948d6996250d0cb716d7445e7e2103d03b94097a77c0e128f9ab7
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:5.8.0, @ethersproject/bignumber@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bignumber@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    bn.js: ^5.2.1
  checksum: c87017f466b32d482e4b39370016cfc3edafc2feb89377011c54cd2e7dd011072ef4f275df59cd9fe080a187066082c1808b2682d97547c4fb9e6912331200c3
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:5.8.0, @ethersproject/bytes@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bytes@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": ^5.8.0
  checksum: 507e8ef1f1559590b4e78e3392a2b16090e96fb1091e0b08d3a8491df65976b313c29cdb412594454f68f9f04d5f77ea5a400b489d80a3e46a608156ef31b251
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:5.8.0, @ethersproject/constants@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/constants@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": ^5.8.0
  checksum: 74830c44f4315a1058b905c73be7a9bb92850e45213cb28a957447b8a100f22a514f4500b0ea5ac7a995427cecef9918af39ae4e0e0ecf77aa4835b1ea5c3432
  languageName: node
  linkType: hard

"@ethersproject/contracts@npm:5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/contracts@npm:5.8.0"
  dependencies:
    "@ethersproject/abi": ^5.8.0
    "@ethersproject/abstract-provider": ^5.8.0
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/address": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
  checksum: cb181012bd55cc19c08f136e56e28e922f1ca66af66747a1b3f58a2aea5b3332bc7ecfe2d23fa14245e7fd45a4fdc4f3427a345c2e9873a9792838cdfe4c62d5
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:5.8.0, @ethersproject/hash@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/hash@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/address": ^5.8.0
    "@ethersproject/base64": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/strings": ^5.8.0
  checksum: e1feb47a98c631548b0f98ef0b1eb1b964bc643d5dea12a0eeb533165004cfcfe6f1d2bb32f31941f0b91e6a82212ad5c8577d6d465fba62c38fc0c410941feb
  languageName: node
  linkType: hard

"@ethersproject/hdnode@npm:5.8.0, @ethersproject/hdnode@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/hdnode@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/basex": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/pbkdf2": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/sha2": ^5.8.0
    "@ethersproject/signing-key": ^5.8.0
    "@ethersproject/strings": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
    "@ethersproject/wordlists": ^5.8.0
  checksum: 72cc6bd218dbe3565b915f3fd8654562003b1b160a5ace8c8959e319333712a0951887641f6888ef91017a39bb804204fc09fb7e5064e3acf76ad701c2ff1266
  languageName: node
  linkType: hard

"@ethersproject/json-wallets@npm:5.8.0, @ethersproject/json-wallets@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/json-wallets@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/address": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/hdnode": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/pbkdf2": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/random": ^5.8.0
    "@ethersproject/strings": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
    aes-js: 3.0.0
    scrypt-js: 3.0.1
  checksum: 8e0f8529f683d0a3fab1c76173bfccf7fc03a27e291344c86797815872722770be787e91f8fa83c37b0abfc47d5f2a2d0eca0ab862effb5539ad545e317f8d83
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:5.8.0, @ethersproject/keccak256@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/keccak256@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    js-sha3: 0.8.0
  checksum: af3621d2b18af6c8f5181dacad91e1f6da4e8a6065668b20e4c24684bdb130b31e45e0d4dbaed86d4f1314d01358aa119f05be541b696e455424c47849d81913
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:5.8.0, @ethersproject/logger@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/logger@npm:5.8.0"
  checksum: 6249885a7fd4a5806e4c8700b76ffcc8f1ff00d71f31aa717716a89fa6b391de19fbb0cb5ae2560b9f57ec0c2e8e0a11ebc2099124c73d3b42bc58e3eedc41d1
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:5.8.0, @ethersproject/networks@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/networks@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": ^5.8.0
  checksum: b1d43fdab13e32be74b5547968c7e54786915a1c3543025c628f634872038750171bef15db0cf42a27e568175b185ac9c185a9aae8f93839452942c5a867c908
  languageName: node
  linkType: hard

"@ethersproject/pbkdf2@npm:5.8.0, @ethersproject/pbkdf2@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/pbkdf2@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/sha2": ^5.8.0
  checksum: 79e06ec6063e745a714c7c3f8ecfb7a8d2db2d19d45ad0e84e59526f685a2704f06e8c8fbfaf3aca85d15037bead7376d704529aac783985e1ff7b90c2d6e714
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:5.8.0, @ethersproject/properties@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/properties@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": ^5.8.0
  checksum: 2bb0369a3c25a7c1999e990f73a9db149a5e514af253e3945c7728eaea5d864144da6a81661c0c414b97be75db7fb15c34f719169a3adb09e585a3286ea78b9c
  languageName: node
  linkType: hard

"@ethersproject/providers@npm:5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/providers@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": ^5.8.0
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/address": ^5.8.0
    "@ethersproject/base64": ^5.8.0
    "@ethersproject/basex": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/hash": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/networks": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/random": ^5.8.0
    "@ethersproject/rlp": ^5.8.0
    "@ethersproject/sha2": ^5.8.0
    "@ethersproject/strings": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
    "@ethersproject/web": ^5.8.0
    bech32: 1.1.4
    ws: 8.18.0
  checksum: 2970ee03fe61bc941555b57075d4a12fbb6342ee56181ad75250a75e9418403e85821bbea1b6e17b25ef35e9eaa1c2b2c564dad7d20af2c1f28ba6db9d0c7ce3
  languageName: node
  linkType: hard

"@ethersproject/random@npm:5.8.0, @ethersproject/random@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/random@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
  checksum: c3bec10516b433eca7ddbd5d97cf2c24153f8fb9615225ea2e3b7fab95a6d6434ab8af55ce55527c3aeb00546ee4363a43aecdc0b5a9970a207ab1551783ddef
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:5.8.0, @ethersproject/rlp@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/rlp@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
  checksum: 9d6f646072b3dd61de993210447d35744a851d24d4fe6262856e372f47a1e9d90976031a9fa28c503b1a4f39dd5ab7c20fc9b651b10507a09b40a33cb04a19f1
  languageName: node
  linkType: hard

"@ethersproject/sha2@npm:5.8.0, @ethersproject/sha2@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/sha2@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    hash.js: 1.1.7
  checksum: ef8916e3033502476fba9358ba1993722ac3bb99e756d5681e4effa3dfa0f0bf0c29d3fa338662830660b45dd359cccb06ba40bc7b62cfd44f4a177b25829404
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:5.8.0, @ethersproject/signing-key@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/signing-key@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    bn.js: ^5.2.1
    elliptic: 6.6.1
    hash.js: 1.1.7
  checksum: 8c07741bc8275568130d97da5d37535c813c842240d0b3409d5e057321595eaf65660c207abdee62e2d7ba225d9b82f0b711ac0324c8c9ceb09a815b231b9f55
  languageName: node
  linkType: hard

"@ethersproject/solidity@npm:5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/solidity@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/sha2": ^5.8.0
    "@ethersproject/strings": ^5.8.0
  checksum: 305166f3f8e8c2f5ad7b0b03ab96d52082fc79b5136601175e1c76d7abd8fd8e3e4b56569dea745dfa2b7fcbfd180c5d824b03fea7e08dd53d515738a35e51dd
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:5.8.0, @ethersproject/strings@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/strings@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/logger": ^5.8.0
  checksum: 997396cf1b183ae66ebfd97b9f98fd50415489f9246875e7769e57270ffa1bffbb62f01430eaac3a0c9cb284e122040949efe632a0221012ee47de252a44a483
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:5.8.0, @ethersproject/transactions@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/transactions@npm:5.8.0"
  dependencies:
    "@ethersproject/address": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/rlp": ^5.8.0
    "@ethersproject/signing-key": ^5.8.0
  checksum: e867516ccc692c3642bfbd34eab6d2acecabb3b964d8e1cced8e450ec4fa490bcf2513efb6252637bc3157ecd5e0250dadd1a08d3ec3150c14478b9ec7715570
  languageName: node
  linkType: hard

"@ethersproject/units@npm:5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/units@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/constants": ^5.8.0
    "@ethersproject/logger": ^5.8.0
  checksum: cc7180c85f695449c20572602971145346fc5c169ee32f23d79ac31cc8c9c66a2049e3ac852b940ddccbe39ab1db3b81e3e093b604d9ab7ab27639ecb933b270
  languageName: node
  linkType: hard

"@ethersproject/wallet@npm:5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/wallet@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": ^5.8.0
    "@ethersproject/abstract-signer": ^5.8.0
    "@ethersproject/address": ^5.8.0
    "@ethersproject/bignumber": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/hash": ^5.8.0
    "@ethersproject/hdnode": ^5.8.0
    "@ethersproject/json-wallets": ^5.8.0
    "@ethersproject/keccak256": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/random": ^5.8.0
    "@ethersproject/signing-key": ^5.8.0
    "@ethersproject/transactions": ^5.8.0
    "@ethersproject/wordlists": ^5.8.0
  checksum: d2921c3212c30a49048e0cba7a8287e0d53a5346ad5a15d46d9932991dc54e541a3da063c47addc1347a4b65142d7239f7056c8716d6f85c8ec4a1bf6b5d2f69
  languageName: node
  linkType: hard

"@ethersproject/web@npm:5.8.0, @ethersproject/web@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/web@npm:5.8.0"
  dependencies:
    "@ethersproject/base64": ^5.8.0
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/strings": ^5.8.0
  checksum: d8ca89bde8777ed1eec81527f8a989b939b4625b2f6c275eea90031637a802ad68bf46911fdd43c5e84ea2962b8a3cb4801ab51f5393ae401a163c17c774123f
  languageName: node
  linkType: hard

"@ethersproject/wordlists@npm:5.8.0, @ethersproject/wordlists@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/wordlists@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": ^5.8.0
    "@ethersproject/hash": ^5.8.0
    "@ethersproject/logger": ^5.8.0
    "@ethersproject/properties": ^5.8.0
    "@ethersproject/strings": ^5.8.0
  checksum: ba24300927a3c9cb85ae8ace84a6be73f3c43aac6eab7a3abe58a7dfd3b168caf3f01a4528efa8193e269dd3d5efe9d4533bdf3b29d5c55743edcb2e864d25d9
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@metamask/eth-sig-util@npm:^5.0.0":
  version: 5.1.0
  resolution: "@metamask/eth-sig-util@npm:5.1.0"
  dependencies:
    "@ethereumjs/util": ^8.0.6
    bn.js: ^4.12.0
    ethereum-cryptography: ^2.0.0
    ethjs-util: ^0.1.6
    tweetnacl: ^1.0.3
    tweetnacl-util: ^0.15.1
  checksum: c639e3bf91625faeb0230a6314f0b2d05e8f5e2989542d3e0eed1d21b7b286e1860f68629870fd7e568c1a599b3993c4210403fb4c84a625fb1e75ef676eab4f
  languageName: node
  linkType: hard

"@noble/curves@npm:1.4.2, @noble/curves@npm:~1.4.0":
  version: 1.4.2
  resolution: "@noble/curves@npm:1.4.2"
  dependencies:
    "@noble/hashes": 1.4.0
  checksum: c475a83c4263e2c970eaba728895b9b5d67e0ca880651e9c6e3efdc5f6a4f07ceb5b043bf71c399fc80fada0b8706e69d0772bffdd7b9de2483b988973a34cba
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.4.0, @noble/hashes@npm:~1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 8ba816ae26c90764b8c42493eea383716396096c5f7ba6bea559993194f49d80a73c081f315f4c367e51bd2d5891700bcdfa816b421d24ab45b41cb03e4f3342
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@polymarket/order-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "@polymarket/order-utils@npm:2.1.0"
  dependencies:
    "@metamask/eth-sig-util": ^5.0.0
    ethers: ^5.7.1
  checksum: 751036e2bcea3c542417fbed1cd81fd1c75481594135b628949449e750cb3f5fdb8e71bcd4129ee2ded175e3da5187709d2ad9ced5808c652cf4a3023f19c1e4
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.6":
  version: 1.1.9
  resolution: "@scure/base@npm:1.1.9"
  checksum: 120820a37dfe9dfe4cab2b7b7460552d08e67dee8057ed5354eb68d8e3440890ae983ce3bee957d2b45684950b454a2b6d71d5ee77c1fd3fddc022e2a510337f
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.4.0":
  version: 1.4.0
  resolution: "@scure/bip32@npm:1.4.0"
  dependencies:
    "@noble/curves": ~1.4.0
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: eff491651cbf2bea8784936de75af5fc020fc1bbb9bcb26b2cfeefbd1fb2440ebfaf30c0733ca11c0ae1e272a2ef4c3c34ba5c9fb3e1091c3285a4272045b0c6
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.3.0":
  version: 1.3.0
  resolution: "@scure/bip39@npm:1.3.0"
  dependencies:
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: dbb0b27df753eb6c6380010b25cc9a9ea31f9cb08864fc51e69e5880ff7e2b8f85b72caea1f1f28af165e83b72c48dd38617e43fc632779d025b50ba32ea759e
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"aes-js@npm:3.0.0":
  version: 3.0.0
  resolution: "aes-js@npm:3.0.0"
  checksum: 251e26d533cd1a915b44896b17d5ed68c24a02484cfdd2e74ec700a309267db96651ea4eb657bf20aac32a3baa61f6e34edf8e2fec2de440a655da9942d334b8
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"append-field@npm:^1.0.0":
  version: 1.0.0
  resolution: "append-field@npm:1.0.0"
  checksum: 482ba08acc0ecef00fe7da6bf2f8e48359a9905ee1af525f3120c9260c02e91eedf0579b59d898e8d8455b6c199e340bc0a2fd4b9e02adaa29a8a86c722b37f9
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"asn1.js@npm:^5.4.1":
  version: 5.4.1
  resolution: "asn1.js@npm:5.4.1"
  dependencies:
    bn.js: ^4.0.0
    inherits: ^2.0.1
    minimalistic-assert: ^1.0.0
    safer-buffer: ^2.1.0
  checksum: 3786a101ac6f304bd4e9a7df79549a7561950a13d4bcaec0c7790d44c80d147c1a94ba3d4e663673406064642a40b23fcd6c82a9952468e386c1a1376d747f9a
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"axios@npm:^0.27.2":
  version: 0.27.2
  resolution: "axios@npm:0.27.2"
  dependencies:
    follow-redirects: ^1.14.9
    form-data: ^4.0.0
  checksum: 38cb7540465fe8c4102850c4368053c21683af85c5fdf0ea619f9628abbcb59415d1e22ebc8a6390d2bbc9b58a9806c874f139767389c862ec9b772235f06854
  languageName: node
  linkType: hard

"axios@npm:^1.6.8":
  version: 1.7.9
  resolution: "axios@npm:1.7.9"
  dependencies:
    follow-redirects: ^1.15.6
    form-data: ^4.0.0
    proxy-from-env: ^1.1.0
  checksum: cb8ce291818effda09240cb60f114d5625909b345e10f389a945320e06acf0bc949d0f8422d25720f5dd421362abee302c99f5e97edec4c156c8939814b23d19
  languageName: node
  linkType: hard

"bagpipe@npm:^0.3.5":
  version: 0.3.5
  resolution: "bagpipe@npm:0.3.5"
  checksum: 70c58ea56a10f226d030896badcfbecb14f1979fe7c59b9d725524fcf13b24ad821fe4f66d1cca7e24107feb5f65b94a1807d69b943f97796334bb83684656ec
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"bcryptjs@npm:^2.4.3":
  version: 2.4.3
  resolution: "bcryptjs@npm:2.4.3"
  checksum: 0e80ed852a41f5dfb1853f53ee14a7390b0ef263ce05dba6e2ef3cd919dfad025a7c21ebcfe5bc7fa04b100990edf90c7a877ff7fe623d3e479753253131b629
  languageName: node
  linkType: hard

"bech32@npm:1.1.4":
  version: 1.1.4
  resolution: "bech32@npm:1.1.4"
  checksum: 0e98db619191548390d6f09ff68b0253ba7ae6a55db93dfdbb070ba234c1fd3308c0606fbcc95fad50437227b10011e2698b89f0181f6e7f845c499bd14d0f4b
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0":
  version: 4.12.1
  resolution: "bn.js@npm:4.12.1"
  checksum: f7f84a909bd07bdcc6777cccbf280b629540792e6965fb1dd1aeafba96e944f197ca10cbec2692f51e0a906ff31da1eb4317f3d1cd659d6f68b8bcd211f7ecbc
  languageName: node
  linkType: hard

"bn.js@npm:^4.11.9, bn.js@npm:^4.12.0":
  version: 4.12.2
  resolution: "bn.js@npm:4.12.2"
  checksum: dd224afda6f5a7d15f2fe5154e1a1c245576a725584ea1852c8c42f9748dfe847bc63a48b2885360023389a24cfebb3653ca97f4c69742f3c22bc63da6565030
  languageName: node
  linkType: hard

"bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: 4384d35fef785c757eb050bc1f13d60dd8e37662ca72392ae6678b35cfa2a2ae8f0494291086294683a7d977609c7878ac3cff08ecca7f74c3ca73f3acbadbe8
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3, body-parser@npm:^1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.5
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.13.0
    raw-body: 2.5.2
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: 1a35c59a6be8d852b00946330141c4f142c6af0f970faa87f10ad74f1ee7118078056706a05ae3093c54dabca9cd3770fa62a170a85801da1a4324f04381167d
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"browser-or-node@npm:^2.1.1":
  version: 2.1.1
  resolution: "browser-or-node@npm:2.1.1"
  checksum: b1650b579345574d2c1905c73d385915a032523066ef9589036cf129b23b27b85096b6713476dffb88feb83cda798ed0774363e699117aa2724a0409adea25aa
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"busboy@npm:^1.0.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 3c55343261bb387c58a4762d15ad9d42053659a62681ec5eb50690c6b52a4a666302a01d557133ce6533e8bd04530ee3b209f23dd06c9577a1925556f8fcccdf
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    get-intrinsic: ^1.2.6
  checksum: a93bbe0f2d0a2d6c144a4349ccd0593d5d0d5d9309b69101710644af8964286420062f2cc3114dca120b9bc8cc07507952d4b1b3ea7672e0d7f6f1675efedb32
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.2":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concat-stream@npm:^1.5.2":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^2.2.2
    typedarray: ^0.0.6
  checksum: 1ef77032cb4459dcd5187bd710d6fc962b067b64ec6a505810de3d2b8cc0605638551b42f8ec91edf6fcd26141b32ef19ad749239b58fae3aba99187adc32285
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"cookie-parser@npm:^1.4.7":
  version: 1.4.7
  resolution: "cookie-parser@npm:1.4.7"
  dependencies:
    cookie: 0.7.2
    cookie-signature: 1.0.6
  checksum: 243fa13f217e793d20a57675e6552beea08c5989fcc68495d543997a31646875335e0e82d687b42dcfd466df57891d22bae7f5ba6ab33b7705ed2dd6eb989105
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.7":
  version: 1.0.7
  resolution: "cookie-signature@npm:1.0.7"
  checksum: 1a62808cd30d15fb43b70e19829b64d04b0802d8ef00275b57d152de4ae6a3208ca05c197b6668d104c4d9de389e53ccc2d3bc6bcaaffd9602461417d8c40710
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: cec5e425549b3650eb5c3498a9ba3cde0b9cd419e3b36e4b92739d30b4d89e0b678b98c1ddc209ce7cf958cd3215671fd6ac47aec21f10c2a0cc68abd399d8a7
  languageName: node
  linkType: hard

"cookie@npm:0.7.2":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 9bf8555e33530affd571ea37b615ccad9b9a34febbf2c950c86787088eb00a8973690833b0f8ebd6b69b753c62669ea60cec89178c1fb007bf0749abed74f93e
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cors@npm:^2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: ^4
    vary: ^1
  checksum: ced838404ccd184f61ab4fdc5847035b681c90db7ac17e428f3d81d69e2989d2b680cc254da0e2554f5ed4f8a341820a1ce3d1c16b499f6e2f47a1b9b07b5006
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:~2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.7":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: c27419b5875a44addcc56cc69b7dc5b0e6587826ca85d5b355da9303c6fc317fc9989f1f18366a16378c9fdd9532d14117a1abe6029cc719cdbbef6eaef2cea4
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"elliptic@npm:6.6.1":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: ^4.11.9
    brorand: ^1.1.0
    hash.js: ^1.0.0
    hmac-drbg: ^1.0.1
    inherits: ^2.0.4
    minimalistic-assert: ^1.0.1
    minimalistic-crypto-utils: ^1.0.1
  checksum: 27b14a52f68bbbc0720da259f712cb73e953f6d2047958cd02fb0d0ade2e83849dc39fb4af630889c67df8817e24237428cf59c4f4c07700f755b401149a7375
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
  checksum: 26f0ff78ab93b63394e8403c353842b2272836968de4eafe97656adfb8a7c84b9099bf0fe96ed58f4a4cddc860f6e34c77f91649a58a5daa4a9c40b902744e3c
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0":
  version: 2.2.1
  resolution: "ethereum-cryptography@npm:2.2.1"
  dependencies:
    "@noble/curves": 1.4.2
    "@noble/hashes": 1.4.0
    "@scure/bip32": 1.4.0
    "@scure/bip39": 1.3.0
  checksum: 1466e4c417b315a6ac67f95088b769fafac8902b495aada3c6375d827e5a7882f9e0eea5f5451600d2250283d9198b8a3d4d996e374e07a80a324e29136f25c6
  languageName: node
  linkType: hard

"ethers@npm:^5.7.1":
  version: 5.8.0
  resolution: "ethers@npm:5.8.0"
  dependencies:
    "@ethersproject/abi": 5.8.0
    "@ethersproject/abstract-provider": 5.8.0
    "@ethersproject/abstract-signer": 5.8.0
    "@ethersproject/address": 5.8.0
    "@ethersproject/base64": 5.8.0
    "@ethersproject/basex": 5.8.0
    "@ethersproject/bignumber": 5.8.0
    "@ethersproject/bytes": 5.8.0
    "@ethersproject/constants": 5.8.0
    "@ethersproject/contracts": 5.8.0
    "@ethersproject/hash": 5.8.0
    "@ethersproject/hdnode": 5.8.0
    "@ethersproject/json-wallets": 5.8.0
    "@ethersproject/keccak256": 5.8.0
    "@ethersproject/logger": 5.8.0
    "@ethersproject/networks": 5.8.0
    "@ethersproject/pbkdf2": 5.8.0
    "@ethersproject/properties": 5.8.0
    "@ethersproject/providers": 5.8.0
    "@ethersproject/random": 5.8.0
    "@ethersproject/rlp": 5.8.0
    "@ethersproject/sha2": 5.8.0
    "@ethersproject/signing-key": 5.8.0
    "@ethersproject/solidity": 5.8.0
    "@ethersproject/strings": 5.8.0
    "@ethersproject/transactions": 5.8.0
    "@ethersproject/units": 5.8.0
    "@ethersproject/wallet": 5.8.0
    "@ethersproject/web": 5.8.0
    "@ethersproject/wordlists": 5.8.0
  checksum: fb107bf28dc3aedde4729f9553be066c699e0636346c095b4deeb5349a0c0c8538f48a58b5c8cbefced008706919739c5f7b8f4dd506bb471a31edee18cda228
  languageName: node
  linkType: hard

"ethjs-util@npm:^0.1.6":
  version: 0.1.6
  resolution: "ethjs-util@npm:0.1.6"
  dependencies:
    is-hex-prefixed: 1.0.0
    strip-hex-prefix: 1.0.0
  checksum: 1f42959e78ec6f49889c49c8a98639e06f52a15966387dd39faf2930db48663d026efb7db2702dcffe7f2a99c4a0144b7ce784efdbf733f4077aae95de76d65f
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"express-session@npm:^1.18.1":
  version: 1.18.1
  resolution: "express-session@npm:1.18.1"
  dependencies:
    cookie: 0.7.2
    cookie-signature: 1.0.7
    debug: 2.6.9
    depd: ~2.0.0
    on-headers: ~1.0.2
    parseurl: ~1.3.3
    safe-buffer: 5.2.1
    uid-safe: ~2.1.5
  checksum: e712cb3399300d9e300b51769ee3e81da6a4a54acc39137945134bf61a452f27ee9afde337f3c0f300457a88b3a12d0b5c711625684d7c8d998e9d2bd34d9e18
  languageName: node
  linkType: hard

"express@npm:^4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.3
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.7.1
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.3.1
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.3
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.12
    proxy-addr: ~2.0.7
    qs: 6.13.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.19.0
    serve-static: 1.16.2
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 3aef1d355622732e20b8f3a7c112d4391d44e2131f4f449e1f273a309752a41abfad714e881f177645517cbe29b3ccdc10b35e7e25c13506114244a5b72f549d
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: 2.6.9
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    on-finished: 2.4.1
    parseurl: ~1.3.3
    statuses: 2.0.1
    unpipe: ~1.0.0
  checksum: a8c58cd97c9cd47679a870f6833a7b417043f5a288cd6af6d0f49b476c874a506100303a128b6d3b654c3d74fa4ff2ffed68a48a27e8630cda5c918f2977dcf4
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.14.9, follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 859e2bacc7a54506f2bf9aacb10d165df78c8c1b0ceb8023f966621b233717dab56e8d08baadc3ad3b9db58af290413d585c999694b7c146aaf2616340c3d2a6
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: ccee458cd5baf234d6b57f349fe9cc5f9a2ea8fd1af5ecda501a18fd1572a6dd3bf08a49f00568afd995b6a65af34cb8dec083cf9d582c4e621836499498dd84
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-extra@npm:^8.0.1":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: bf44f0e6cea59d5ce071bba4c43ca76d216f89e402dc6285c128abc0902e9b8525135aa808adad72c9d5d218e9f4bcc63962815529ff2f684ad532172a284880
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    get-proto: ^1.0.0
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: a1597b3b432074f805b6a0ba1182130dd6517c0ea0c4eecc4b8834c803913e1ea62dfc412865be795b3dacb1555a21775b70cf9af7a18b1454ff3414e5442d4a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: ^2.0.3
    minimalistic-assert: ^1.0.1
  checksum: e350096e659c62422b85fa508e4b3669017311aa4c49b74f19f8e1bc7f3a54a584fdfd45326d4964d6011f2b2d882e38bea775a96046f2a61b7779a979629d8f
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: ^1.0.3
    minimalistic-assert: ^1.0.0
    minimalistic-crypto-utils: ^1.0.1
  checksum: bd30b6a68d7f22d63f10e1888aee497d7c2c5c0bb469e66bbdac99f143904d1dfe95f8131f95b3e86c86dd239963c9d972fcbe147e7cffa00e55d18585c43fe0
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ignore-by-default@npm:^1.0.1":
  version: 1.0.1
  resolution: "ignore-by-default@npm:1.0.1"
  checksum: 441509147b3615e0365e407a3c18e189f78c07af08564176c680be1fabc94b6c789cad1342ad887175d4ecd5225de86f73d376cec8e06b42fd9b429505ffcf8a
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-hex-prefixed@npm:1.0.0":
  version: 1.0.0
  resolution: "is-hex-prefixed@npm:1.0.0"
  checksum: 5ac58e6e528fb029cc43140f6eeb380fad23d0041cc23154b87f7c9a1b728bcf05909974e47248fd0b7fcc11ba33cf7e58d64804883056fabd23e2b898be41de
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 75df77c1fc266973f06cce8309ce010e9e9f07ec35ab12022ed29b7f0d9c8757f5a73e1b35aa24840dced0dea7059085aa143d817aea9e188e2a80d569d9adce
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 6447d6224f0d31623eef9b51185af03ac328a7553efcee30fa423d98a9e276ca08db87d71e17f2310b0263fd3ffa6c2a90a6308367f661dc21580f9469897c9e
  languageName: node
  linkType: hard

"kruptein@npm:^2.0.4":
  version: 2.2.3
  resolution: "kruptein@npm:2.2.3"
  dependencies:
    asn1.js: ^5.4.1
  checksum: b4f082edf184d2b54b323b059b8bb0fd26aed84743740cda8a8352a09bc7346981565dbb81ecc4d67b7c9a52f1be264be1a59b02c746ba32c6dd0439c88b7cf0
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 52117adbe0313d5defa771c9993fe081e2d2df9b840597e966aadafde04ae8d0e3da46bac7ca4efc37d4d2b839436582659cd49c6a43eacb3fe3050896a105d1
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 0e496547253a36e98a83fb00c628c53c3fb540fa5aaeaf718438873785afd193244988c09d219bb1802984ff227d04938d9571ef90fe82b48bd282262586aaff
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.4":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multer@npm:^1.4.5-lts.1":
  version: 1.4.5-lts.1
  resolution: "multer@npm:1.4.5-lts.1"
  dependencies:
    append-field: ^1.0.0
    busboy: ^1.0.0
    concat-stream: ^1.5.2
    mkdirp: ^0.5.4
    object-assign: ^4.1.1
    type-is: ^1.6.4
    xtend: ^4.0.0
  checksum: d6dfa78a6ec592b74890412f8962da8a87a3dcfe20f612e039b735b8e0faa72c735516c447f7de694ee0d981eb0a1b892fb9e2402a0348dc6091d18c38d89ecc
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"nodemon@npm:^3.1.7":
  version: 3.1.9
  resolution: "nodemon@npm:3.1.9"
  dependencies:
    chokidar: ^3.5.2
    debug: ^4
    ignore-by-default: ^1.0.1
    minimatch: ^3.1.2
    pstree.remy: ^1.1.8
    semver: ^7.5.3
    simple-update-notifier: ^2.0.0
    supports-color: ^5.5.0
    touch: ^3.1.0
    undefsafe: ^2.0.5
  bin:
    nodemon: bin/nodemon.js
  checksum: d045065dea08904f1356d18132538e71a61df12cb4e2852730310492943676d4789bedb28c343a5d85d5e07558bf47b73f000a8017409f0b7d522a3c1c42b2e5
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 2cfc65e7ee38af2e04aea98f054753b0230011c0eeca4ecf131bd7d25984cbbf6f214586e0ae5dfcc2e830bc0bffa5a7fb28ea8d0b306ffd4ae8ea2d814c1ab3
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.3
  resolution: "object-inspect@npm:1.13.3"
  checksum: 8c962102117241e18ea403b84d2521f78291b774b03a29ee80a9863621d88265ffd11d0d7e435c4c2cea0dc2a2fbf8bbc92255737a05536590f2df2e8756f297
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: ab237858bee7b25ecd885189f175ab5b5161e7b712b360d44f5c4516b8d271da3e4bf7bf0a7b9153ecb04c7d90ce8ff5158614e1208819cf62bac2b08452722e
  languageName: node
  linkType: hard

"pedone-clob-client@npm:^1.0.1":
  version: 1.0.1
  resolution: "pedone-clob-client@npm:1.0.1"
  dependencies:
    "@polymarket/order-utils": ^2.1.0
    axios: ^0.27.2
    browser-or-node: ^2.1.1
    ethers: ^5.7.1
  checksum: 4488c8018dcdf2e6cb32ad21c57b998b8fc75b65f4200103cb9e475dcc042396fc1596402eb28e1da9cd0807bb5bdc6cd489ed3ae9f9a381dbb4804d2d619095
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: ed7fcc2ba0a33404958e34d95d18638249a68c430e30fcb6c478497d72739ba64ce9810a24f53a7d921d0c065e5b78e3822759800698167256b04659366ca4d4
  languageName: node
  linkType: hard

"pstree.remy@npm:^1.1.8":
  version: 1.1.8
  resolution: "pstree.remy@npm:1.1.8"
  checksum: 5cb53698d6bb34dfb278c8a26957964aecfff3e161af5fbf7cee00bbe9d8547c7aced4bd9cb193bce15fb56e9e4220fc02a5bf9c14345ffb13a36b858701ec2d
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: ^1.0.6
  checksum: e9404dc0fc2849245107108ce9ec2766cde3be1b271de0bf1021d049dc5b98d1a2901e67b431ac5509f865420a7ed80b7acb3980099fe1c118a1c5d2e1432ad8
  languageName: node
  linkType: hard

"random-bytes@npm:~1.0.0":
  version: 1.0.0
  resolution: "random-bytes@npm:1.0.0"
  checksum: 09faa256394aa2ca9754aa57e92a27c452c3e97ffb266e98bebb517332e9df7168fea393159f88d884febce949ba8bec8ddb02f03342da6c6023ecc7b155e0ae
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: ba1583c8d8a48e8fbb7a873fdbb2df66ea4ff83775421bfe21ee120140949ab048200668c47d9ae3880012f6e217052690628cf679ddfbd82c9fc9358d574676
  languageName: node
  linkType: hard

"readable-stream@npm:^2.2.2":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scrypt-js@npm:3.0.1":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: b7c7d1a68d6ca946f2fbb0778e0c4ec63c65501b54023b2af7d7e9f48fdb6c6580d6f7675cd53bda5944c5ebc057560d5a6365079752546865defb3b79dea454
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 5ae11bd900c1c2575525e2aa622e856804e2f96a09281ec1e39610d089f53aa69e13fd8db84b52f001d0318cf4bb0b3b904ad532fc4c0014eb90d32db0cff55f
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.19.0
  checksum: dffc52feb4cc5c68e66d0c7f3c1824d4e989f71050aefc9bd5f822a42c54c9b814f595fc5f2b717f4c7cc05396145f3e90422af31186a93f76cf15f707019759
  languageName: node
  linkType: hard

"server@workspace:.":
  version: 0.0.0-use.local
  resolution: "server@workspace:."
  dependencies:
    axios: ^1.6.8
    bcryptjs: ^2.4.3
    body-parser: ^1.20.3
    cookie-parser: ^1.4.7
    cors: ^2.8.5
    dotenv: ^16.4.7
    express: ^4.21.2
    express-session: ^1.18.1
    multer: ^1.4.5-lts.1
    nodemon: ^3.1.7
    pedone-clob-client: ^1.0.1
    session-file-store: ^1.0.1
  languageName: unknown
  linkType: soft

"session-file-store@npm:^1.0.1":
  version: 1.5.0
  resolution: "session-file-store@npm:1.5.0"
  dependencies:
    bagpipe: ^0.3.5
    fs-extra: ^8.0.1
    kruptein: ^2.0.4
    object-assign: ^4.1.1
    retry: ^0.12.0
    write-file-atomic: 3.0.3
  checksum: e101f173c91c000e855035794162ccfd0eab1e9a38bf984742f562678a2531f6ef808879f337cb2f852b9256760d8532ab9d732a12786f541f08567f39a65d60
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-update-notifier@npm:^2.0.0":
  version: 2.0.0
  resolution: "simple-update-notifier@npm:2.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: 9ba00d38ce6a29682f64a46213834e4eb01634c2f52c813a9a7b8873ca49cdbb703696f3290f3b27dc067de6d9418b0b84bef22c3eb074acf352529b2d6c27fd
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-hex-prefix@npm:1.0.0":
  version: 1.0.0
  resolution: "strip-hex-prefix@npm:1.0.0"
  dependencies:
    is-hex-prefixed: 1.0.0
  checksum: 4cafe7caee1d281d3694d14920fd5d3c11adf09371cef7e2ccedd5b83efd9e9bd2219b5d6ce6e809df6e0f437dc9d30db1192116580875698aad164a6d6b285b
  languageName: node
  linkType: hard

"supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"touch@npm:^3.1.0":
  version: 3.1.1
  resolution: "touch@npm:3.1.1"
  bin:
    nodetouch: bin/nodetouch.js
  checksum: fb8c54207500eb760b6b9d77b9c5626cc027c9ad44431eed4268845f00f8c6bbfc95ce7e9da8e487f020aa921982a8bc5d8e909d0606e82686bd0a08a8e0539b
  languageName: node
  linkType: hard

"tweetnacl-util@npm:^0.15.1":
  version: 0.15.1
  resolution: "tweetnacl-util@npm:0.15.1"
  checksum: ae6aa8a52cdd21a95103a4cc10657d6a2040b36c7a6da7b9d3ab811c6750a2d5db77e8c36969e75fdee11f511aa2b91c552496c6e8e989b6e490e54aca2864fc
  languageName: node
  linkType: hard

"tweetnacl@npm:^1.0.3":
  version: 1.0.3
  resolution: "tweetnacl@npm:1.0.3"
  checksum: e4a57cac188f0c53f24c7a33279e223618a2bfb5fea426231991652a13247bea06b081fd745d71291fcae0f4428d29beba1b984b1f1ce6f66b06a6d1ab90645c
  languageName: node
  linkType: hard

"type-is@npm:^1.6.4, type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: ^1.0.0
  checksum: 99c11aaa8f45189fcfba6b8a4825fd684a321caa9bd7a76a27cf0c7732c174d198b99f449c52c3818107430b5f41c0ccbbfb75cb2ee3ca4a9451710986d61a60
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 33b39f3d0e8463985eeaeeacc3cb2e28bc3dfaf2a5ed219628c0b629d5d7b810b0eb2165f9f607c34871d5daa92ba1dc69f49051cf7d578b4cbd26c340b9d1b1
  languageName: node
  linkType: hard

"uid-safe@npm:~2.1.5":
  version: 2.1.5
  resolution: "uid-safe@npm:2.1.5"
  dependencies:
    random-bytes: ~1.0.0
  checksum: 07536043da9a026f4a2bc397543d0ace7587449afa1d9d2c4fd3ce76af8a5263a678788bcc429dff499ef29d45843cd5ee9d05434450fcfc19cc661229f703d1
  languageName: node
  linkType: hard

"undefsafe@npm:^2.0.5":
  version: 2.0.5
  resolution: "undefsafe@npm:2.0.5"
  checksum: f42ab3b5770fedd4ada175fc1b2eb775b78f609156f7c389106aafd231bfc210813ee49f54483d7191d7b76e483bc7f537b5d92d19ded27156baf57592eb02cc
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"write-file-atomic@npm:3.0.3":
  version: 3.0.3
  resolution: "write-file-atomic@npm:3.0.3"
  dependencies:
    imurmurhash: ^0.1.4
    is-typedarray: ^1.0.0
    signal-exit: ^3.0.2
    typedarray-to-buffer: ^3.1.5
  checksum: c55b24617cc61c3a4379f425fc62a386cc51916a9b9d993f39734d005a09d5a4bb748bc251f1304e7abd71d0a26d339996c275955f527a131b1dcded67878280
  languageName: node
  linkType: hard

"ws@npm:8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 91d4d35bc99ff6df483bdf029b9ea4bfd7af1f16fc91231a96777a63d263e1eabf486e13a2353970efc534f9faa43bdbf9ee76525af22f4752cbc5ebda333975
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard
