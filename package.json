{"name": "pdone_backstage", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext ts,tsx . && tsc", "format": "prettier \"**/*\" --write --ignore-unknown --cache", "start": "node index.js", "dev:server": "nodemon index.js"}, "dependencies": {"@ant-design/icons": "^5.3.6", "@types/papaparse": "^5.3.15", "antd": "^5.16.1", "axios": "^1.7.9", "buffer": "^6.0.3", "dayjs": "^1.11.13", "ethers": "^6.13.4", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "papaparse": "^5.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.3", "react-mde": "^11.5.0", "react-router-dom": "^7.1.3", "siwe": "^2.3.2", "uuid": "^11.1.0", "vite-plugin-rewrite-all": "^1.0.2"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.14", "@types/node": "^22.9.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.2.24", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.6.0", "@typescript-eslint/parser": "^7.6.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "nodemon": "^2.0.22", "postcss": "^8.4.49", "prettier": "^3.2.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.5", "vite": "^6.0.7"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@3.2.3"}