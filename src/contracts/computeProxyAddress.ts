import { Contract, JsonRpc<PERSON><PERSON><PERSON>, getAddress } from 'ethers'; // 修正 utils 引用
import Papa from 'papapar<PERSON>';

// 配置
const proxyFactoryAddress = '******************************************';
if (!proxyFactoryAddress) {
  throw new Error('VITE_NEXT_PUBLIC_FACTORY_ADDRESS is not defined');
}

const abi = ['function computeProxyAddress(address user) public view returns (address)'];

async function computeProxyAddress(address: string): Promise<string> {
  try {
    const providerUrl = 'https://ethereum-sepolia-rpc.publicnode.com';
    const provider = new JsonRpcProvider(providerUrl);

    // 创建合约实例并调用方法
    const contract = new Contract(proxyFactoryAddress as string, abi, provider);
    return await contract.computeProxyAddress(address);
  } catch (error) {
    console.error('Error fetching proxy address:', error);
    return '';
  }
}

async function computeProxyAddresses(addresses: string[]): Promise<string[]> {
  try {
    const providerUrl = 'https://ethereum-sepolia-rpc.publicnode.com';
    const provider = new JsonRpcProvider(providerUrl);

    const contract = new Contract(proxyFactoryAddress as string, abi, provider);

    // 校验和格式化地址
    const formattedAddresses = addresses
      .map((address) => {
        try {
          return getAddress(address.toLowerCase()); // 使用 getAddress 校验和格式化地址
        } catch (error) {
          console.error(`Invalid address skipped: ${address}`);
          return null; // 跳过无效地址
        }
      })
      .filter((address) => address !== null); // 过滤掉无效地址

    const results = await Promise.all(
      formattedAddresses.map((address) => contract.computeProxyAddress(address!))
    );
    return results;
  } catch (error) {
    console.error('Error fetching proxy addresses:', error);
    return [];
  }
}

async function computeProxyAddressesFromCsv(filePath: string): Promise<{ [key: string]: string }> {
  const addresses: string[] = [];
  const results: { [key: string]: string } = {};

  return new Promise((resolve, reject) => {
    Papa.parse(filePath, {
      download: true, // 允许从文件路径下载 CSV 文件
      header: true,
      skipEmptyLines: true,
      complete: async (parseResult: Papa.ParseResult<{ address: string }>) => {
        try {
          parseResult.data.forEach((row) => {
            if (row.address) {
              try {
                addresses.push(getAddress(row.address.toLowerCase())); // 使用 getAddress 校验和格式化地址
              } catch (error) {
                console.error(`Invalid address in CSV skipped: ${row.address}`);
              }
            }
          });

          const proxyAddresses = await computeProxyAddresses(addresses);
          addresses.forEach((address, index) => {
            results[address] = proxyAddresses[index];
          });
          resolve(results);
        } catch (error) {
          reject(error);
        }
      },
      error: (error) => {
        reject(error);
      },
    });
  });
}

export { computeProxyAddress, computeProxyAddresses, computeProxyAddressesFromCsv };
