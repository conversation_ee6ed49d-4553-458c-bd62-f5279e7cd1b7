import React, { useEffect } from 'react';
import { Form, FormInstance } from 'antd';
import MarkdownEditor from '@/components/MarkdownEditor';

interface RulesStepProps {
  form: FormInstance;
  stepData: any;
  mdValue: string;
  setMdValue: (value: string) => void;
}

const RulesStep: React.FC<RulesStepProps> = ({ form, stepData, mdValue, setMdValue }) => {
  // 初始化表单值
  useEffect(() => {
    if (stepData.rules || stepData.mdValue) {
      const initialValue = stepData.mdValue || stepData.rules || '';
      if (initialValue !== mdValue) {
        setMdValue(initialValue);
        form.setFieldsValue({ rules: initialValue });
      }
    }
  }, [stepData, form, setMdValue, mdValue]);

  // 默认规则模板
  const defaultRules = `# Event Rules

## Participation Guidelines
- All participants must follow community guidelines
- Predictions must be made before the event deadline
- Only one prediction per user is allowed

## Scoring System
- Points are awarded based on prediction accuracy
- Bonus points may be given for early predictions
- Final scores are calculated after event conclusion

## Important Notes
- Event organizers reserve the right to modify rules if necessary
- Disputes will be resolved by the moderation team
- Have fun and predict responsibly!

---
*Last updated: ${new Date().toLocaleDateString()}*`;

  const handleUseTemplate = () => {
    setMdValue(defaultRules);
    form.setFieldsValue({ rules: defaultRules });
  };

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}
      </style>

      <div style={{ width: '96%', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #f59e0b, #d97706)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #ef4444, #dc2626)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(245, 158, 11, 0.3)',
              }}
            >
              📋
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  marginBottom: '6px',
                  letterSpacing: '0.4px',
                }}
              >
                Event Rules
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                Define participation guidelines and terms
              </div>
            </div>
          </div>

          {/* 表单内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '32px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* Markdown提示和模板按钮 */}
            <div
              style={{
                background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
                border: '2px solid #3b82f6',
                borderRadius: '16px',
                padding: '20px',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)',
                position: 'relative',
                overflow: 'hidden',
                animation: 'slideInUp 0.6s ease-out',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-10px',
                  right: '-10px',
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                  borderRadius: '50%',
                  opacity: '0.1',
                }}
              ></div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: '18px',
                      fontWeight: '700',
                      color: '#1e40af',
                      marginBottom: '4px',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Markdown Support
                  </div>
                  <div
                    style={{
                      fontSize: '16px',
                      color: '#1e40af',
                      fontWeight: '500',
                      opacity: '0.9',
                    }}
                  >
                    Use Markdown formatting for better readability
                  </div>
                </div>
              </div>

              <button
                onClick={handleUseTemplate}
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  zIndex: 1,
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(-2px)';
                  target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(0)';
                  target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                }}
              >
                📝 Use Template
              </button>
            </div>

            {/* 规则编辑器 */}
            <div>
              <Form.Item
                name="rules"
                label={
                  <span
                    style={{
                      fontSize: '24px',
                      fontWeight: '700',
                      color: '#1f2937',
                      letterSpacing: '0.3px',
                    }}
                  >
                    📋 Rules
                  </span>
                }
                rules={[
                  { required: true, message: 'Rules are required' },
                  { min: 50, message: 'At least 50 characters' },
                ]}
              >
                <div
                  style={{
                    border: '2px solid #e5e7eb',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                  }}
                >
                  <MarkdownEditor value={mdValue} onChange={setMdValue} needToolBar={true} />
                </div>
              </Form.Item>

              <p
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                  fontStyle: 'italic',
                  margin: 0,
                }}
              >
                Define participation guidelines, scoring system, and important terms
              </p>
            </div>

            {/* 重要警告 */}
            <div
              style={{
                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                border: '2px solid #f59e0b',
                borderRadius: '16px',
                padding: '16px',
                boxShadow: '0 4px 12px rgba(245, 158, 11, 0.15)',
                position: 'relative',
                overflow: 'hidden',
                animation: 'slideInUp 0.8s ease-out',
                textAlign: 'center',
              }}
            >
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-10px',
                  right: '-10px',
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #f59e0b, #d97706)',
                  borderRadius: '50%',
                  opacity: '0.1',
                }}
              ></div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: '18px',
                      fontWeight: '700',
                      color: '#92400e',
                      marginBottom: '4px',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Important Notice
                  </div>
                  <div
                    style={{
                      fontSize: '16px',
                      color: '#92400e',
                      fontWeight: '500',
                      opacity: '0.9',
                    }}
                  >
                    Rules can't be changed after users start participating
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RulesStep;
