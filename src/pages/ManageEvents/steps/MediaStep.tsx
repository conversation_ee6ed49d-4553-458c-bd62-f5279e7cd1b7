import React, { useEffect } from 'react';
import { Form, DatePicker, Row, Col, FormInstance } from 'antd';
import ImageUpload from '@/components/ImageUpload';

const { RangePicker } = DatePicker;

interface MediaStepProps {
  form: FormInstance;
  stepData: any;
}

const MediaStep: React.FC<MediaStepProps> = ({ form, stepData }) => {
  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};

    if (stepData.icon) {
      // 确保文件对象有url属性
      const iconFiles = Array.isArray(stepData.icon) ? stepData.icon : [stepData.icon];
      const processedIconFiles = iconFiles.map((file: any) => {
        if (file && !file.url && file.originFileObj) {
          return {
            ...file,
            url:
              file.thumbUrl ||
              (file.originFileObj ? URL.createObjectURL(file.originFileObj) : undefined),
          };
        }
        return file;
      });
      initialValues.icon = processedIconFiles;
    }
    if (stepData.image) {
      // 确保文件对象有url属性
      const imageFiles = Array.isArray(stepData.image) ? stepData.image : [stepData.image];
      const processedImageFiles = imageFiles.map((file: any) => {
        if (file && !file.url && file.originFileObj) {
          // 如果没有url但有originFileObj，重新生成base64 url
          return {
            ...file,
            url:
              file.thumbUrl ||
              (file.originFileObj ? URL.createObjectURL(file.originFileObj) : undefined),
          };
        }
        return file;
      });
      initialValues.image = processedImageFiles;
    }
    if (stepData.date_range) {
      initialValues.date_range = stepData.date_range;
    }

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [stepData, form]);

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          @keyframes pulse {
            0%, 100% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
          }
        `}
      </style>

      {/* 整体表单容器 */}
      <div
        style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '2px solid #e2e8f0',
          borderRadius: '24px',
          padding: '40px',
          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
          position: 'relative',
          overflow: 'hidden',
          width: '96%',
          margin: '0 auto',
          height: '100%',
        }}
      >
        {/* 装饰性背景元素 */}
        <div
          style={{
            position: 'absolute',
            top: '-40px',
            right: '-40px',
            width: '120px',
            height: '120px',
            background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
            borderRadius: '50%',
            opacity: '0.04',
          }}
        ></div>
        <div
          style={{
            position: 'absolute',
            bottom: '-50px',
            left: '-50px',
            width: '150px',
            height: '150px',
            background: 'linear-gradient(135deg, #06b6d4, #0891b2)',
            borderRadius: '50%',
            opacity: '0.03',
          }}
        ></div>

        {/* 表单标题 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            marginBottom: '20px',
            paddingBottom: '20px',
            borderBottom: '3px solid #e2e8f0',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              width: '56px',
              height: '56px',
              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
              borderRadius: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              boxShadow: '0 6px 16px rgba(139, 92, 246, 0.3)',
            }}
          >
            🎨
          </div>
          <div>
            <div
              style={{
                fontSize: '28px',
                fontWeight: '800',
                color: '#1f2937',
                letterSpacing: '0.4px',
              }}
            >
              Media & Schedule
            </div>
            <div
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
              }}
            >
              Upload images and set event timing
            </div>
          </div>
        </div>

        {/* 图片上传区域 */}
        <div
          style={{
            marginBottom: '40px',
            position: 'relative',
            zIndex: 1,
            animation: 'slideInUp 0.6s ease-out',
          }}
        >
          <Row gutter={24} align="top">
            <Col span={12}>
              {/* 标题 */}
              <div
                style={{
                  textAlign: 'center',
                  marginBottom: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '28px',
                }}
              >
                <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                <span
                  style={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  🎯 Event Icon
                </span>
              </div>

              {/* 图片上传组件 */}
              <Form.Item
                name="icon"
                valuePropName="fileList"
                validateTrigger={['onChange']}
                rules={[
                  {
                    required: true,
                    message: 'Please upload an icon for your event',
                  },
                  {
                    validator: (_, value) => {
                      if (!value || (Array.isArray(value) && value.length === 0)) {
                        return Promise.reject(new Error('Please upload an icon'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                style={{ marginBottom: '8px', textAlign: 'center' }}
              >
                <ImageUpload prefix="event" maxSizeKB={64} />
              </Form.Item>

              {/* 文件大小提示 */}
              <div
                style={{
                  fontSize: '12px',
                  color: '#ef4444',
                  fontWeight: '600',
                  textAlign: 'center',
                  minHeight: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                Max 64KB
              </div>
            </Col>

            <Col span={12}>
              {/* 标题 */}
              <div
                style={{
                  textAlign: 'center',
                  marginBottom: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '28px',
                }}
              >
                <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>

                <span
                  style={{
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  🖼️ Event Image
                </span>
              </div>

              {/* 图片上传组件 */}
              <Form.Item
                name="image"
                valuePropName="fileList"
                validateTrigger={['onChange']}
                rules={[
                  {
                    required: true,
                    message: 'Please upload a banner for your event',
                  },
                  {
                    validator: (_, value) => {
                      if (!value || (Array.isArray(value) && value.length === 0)) {
                        return Promise.reject(new Error('Please upload a banner'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                style={{ marginBottom: '8px', textAlign: 'center' }}
              >
                <ImageUpload prefix="event" maxSizeKB={64} />
              </Form.Item>

              {/* 文件大小提示 */}
              <div
                style={{
                  fontSize: '12px',
                  color: '#ef4444',
                  fontWeight: '600',
                  textAlign: 'center',
                  minHeight: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                Max 64KB
              </div>
            </Col>
          </Row>
        </div>

        {/* 日期选择区域 */}
        <div
          style={{
            marginBottom: '16px',
            position: 'relative',
            zIndex: 1,
            animation: 'slideInUp 0.8s ease-out',
          }}
        >
          <Form.Item
            name="date_range"
            label={
              <span
                style={{
                  fontSize: '18px',
                  fontWeight: '700',
                  color: '#1f2937',
                  letterSpacing: '0.3px',
                }}
              >
                ⏰ Date Range
              </span>
            }
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please select start and end dates for your event',
              },
              {
                validator: (_, value) => {
                  if (!value || !Array.isArray(value) || value.length !== 2) {
                    return Promise.reject(new Error('Please select both start and end dates'));
                  }
                  if (value[0] && value[1] && value[0].isAfter(value[1])) {
                    return Promise.reject(new Error('Start date must be before end date'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{ marginBottom: '16px' }}
          >
            <RangePicker
              size="large"
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              placeholder={['Start time', 'End time']}
              onChange={(dates) => {
                form.setFieldsValue({ date_range: dates });
              }}
              style={{
                width: '100%',
                fontSize: '16px',
                borderRadius: '12px',
                border: '2px solid #e5e7eb',
                padding: '12px 16px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                transition: 'all 0.2s ease',
              }}
            />
          </Form.Item>
        </div>

        {/* 时区提示 */}
        <div
          style={{
            textAlign: 'center',
            marginTop: '24px',
            padding: '12px',
            background: '#f0f9ff',
            borderRadius: '8px',
            fontSize: '14px',
            color: '#0369a1',
            fontWeight: '500',
          }}
        >
          🌍 Times are in UTC timezone
        </div>
      </div>
    </>
  );
};

export default MediaStep;
