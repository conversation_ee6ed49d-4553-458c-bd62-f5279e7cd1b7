import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Image,
  App,
  Row,
  Col,
  Card,
  Typography,
  Tooltip,
  Select,
  Checkbox,
  Dropdown,
  Tag,
} from 'antd';
import type { MenuProps } from 'antd';
import axios from 'axios';
import { getCookie } from '@/utils';
import type { ColumnsType } from 'antd/es/table';
import {
  fetchEventsList,
  fetchEventsListBySearch,
  fetchAddEvent,
  fetchAddEventTags,
  fetchGetEventTags,
  fetchUpdateEvent,
  fetchAddSearchEvent,
  fetchUserEventCount,
} from '@/api/events';
import { EventDataType } from '@/types/events';
import {
  formatDate,
  decodeEventData,
  getLocalStorage,
  generateUniqueSlug,
  encodeContent,
  validateJWTWithMessage,
} from '@/utils';
import { PoweroffOutlined, MoreOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import AddModal from './AddModal';
import DetailModal from './DetailModal';
import WarningModal from './WarningModal';
import CreateBannerModal from './CreateBannerModal';
import { useNavigate } from 'react-router-dom';

// 状态颜色映射
const statusColorMap = {
  yes: 'success' as const,
  no: 'error' as const,
};

const ManageEvents: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [copyData, setCopyData] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [eventId, setEventId] = useState<number | undefined>(undefined);
  const [isWarningModalVisible, setIsWarningModalVisible] = useState(false);
  const [warningModalType, setWarningModalType] = useState<'delete' | 'publish'>('delete');
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [currentFilter, setCurrentFilter] = useState<string | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const [currentRecord, setCurrentRecord] = useState<EventDataType | null>(null);
  const [loading, setLoading] = useState<{ [key: number]: boolean }>({});
  const [copyLoading, setCopyLoading] = useState<{ [key: number]: boolean }>({});

  // Banner相关状态
  const [selectedEventId, setSelectedEventId] = useState<number | null>(null);
  const [isBannerModalVisible, setIsBannerModalVisible] = useState(false);

  // 事件数量状态（仅对event_writer有效）
  const [eventCount, setEventCount] = useState<{ created: number; remaining: number }>({
    created: 0,
    remaining: 3,
  });

  const userWallet = getLocalStorage('userWallet');
  const userRole = getCookie('jwt-role');
  const { message } = App.useApp();
  const navigate = useNavigate();

  const fetchData = useCallback(
    async (label?: string, page: number = 1, pageSize: number = 20, filter?: string) => {
      const offset = (page - 1) * pageSize;
      const result = label
        ? await fetchEventsListBySearch(label)
        : await fetchEventsList(userWallet, pageSize, offset, filter);

      const initResult = decodeEventData(result.data.events).sort((a, b) => b.id - a.id);
      setCopyData(result.data.events.sort((a: any, b: any) => b.id - a.id));
      setData(initResult);
      setFilteredData(initResult); // 直接使用服务端筛选的结果

      // 更新分页信息
      if (result.data.events_aggregate) {
        setPagination((prev) => ({
          ...prev,
          current: page,
          pageSize: pageSize,
          total: result.data.events_aggregate.aggregate.count,
        }));
      }

      // 如果是event_writer，同时更新事件数量
      if (userRole === 'event_writer' && userWallet) {
        try {
          const count = await fetchUserEventCount(userWallet);
          setEventCount(count);
        } catch (error) {
          console.error('Failed to fetch user event count:', error);
        }
      }
    },
    [userWallet]
  );

  // 获取用户事件数量（仅对event_writer有效）
  const fetchUserEventCountData = useCallback(async () => {
    if (userRole === 'event_writer' && userWallet) {
      try {
        const count = await fetchUserEventCount(userWallet);
        setEventCount(count);
      } catch (error) {
        console.error('Failed to fetch user event count:', error);
      }
    }
  }, [userRole, userWallet]);

  useEffect(() => {
    fetchData(undefined, pagination.current, pagination.pageSize, currentFilter);
    fetchUserEventCountData(); // 同时获取用户事件数量
  }, [fetchUserEventCountData]);

  const handleFilterChange = (value: string | undefined) => {
    const filterValue = value === 'all' ? undefined : value;
    setCurrentFilter(filterValue);
    // 重新获取数据，应用服务端筛选
    fetchData(undefined, 1, pagination.pageSize, filterValue);
    // 重置分页到第一页
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleTableChange = (paginationInfo: any) => {
    fetchData(undefined, paginationInfo.current, paginationInfo.pageSize, currentFilter);
  };

  // 处理添加Event按钮点击，包含JWT验证
  const handleAddEventClick = () => {
    if (!validateJWTWithMessage('create a new event')) {
      return;
    }
    setIsModalVisible(true);
  };

  // 处理添加Question按钮点击，包含JWT验证
  const handleAddQuestionClick = (eventId: number) => {
    if (!validateJWTWithMessage('add questions to this event')) {
      return;
    }
    navigate(`/questions?eventId=${eventId}`);
  };

  // Banner相关处理函数
  const handleCreateBanner = () => {
    if (!selectedEventId) {
      message.warning('Please select an event first');
      return;
    }
    setIsBannerModalVisible(true);
  };

  const handleBannerModalCancel = () => {
    setIsBannerModalVisible(false);
  };

  const handleAddEvent = async (eventParams: any, selectedTagIdList: any, fullTitle?: string) => {
    try {
      const eventCb = await fetchAddEvent(eventParams);

      // 去重标签ID并转换为数字类型
      const uniqueTagIds = [...new Set(selectedTagIdList.map((id: any) => Number(id)))];

      // 串行添加标签，避免并发请求导致的重复
      for (const tag_id of uniqueTagIds) {
        try {
          const tagParams = { event_id: eventCb.id, tag_id: tag_id as number };
          await fetchAddEventTags(tagParams);
        } catch (error) {
          // 如果是重复标签错误，跳过继续处理下一个
          if (
            axios.isAxiosError(error) &&
            error.response?.data?.code === 'constraint-violation' &&
            error.response?.data?.error?.includes('event_tags_event_id_tag_id_key')
          ) {
            console.warn(`Tag ${tag_id} already exists for event ${eventCb.id}, skipping...`);
            continue;
          }
          // 其他错误则抛出
          throw error;
        }
      }

      if (fullTitle) {
        try {
          await fetchAddSearchEvent(eventCb.id, fullTitle);
        } catch (error) {
          console.warn('Failed to add search event:', error);
        }
      }
    } catch (error: any) {
      // 重新抛出错误，让AddModal处理
      throw error;
    }
  };

  const handleEditEvent = async (eventParams: any) => {
    try {
      if (currentRecord?.id !== undefined) {
        await fetchUpdateEvent(currentRecord.id, eventParams);
        message.success('Event edited successfully');
      } else {
        message.error('Event ID is undefined');
      }
      fetchData(undefined, pagination.current, pagination.pageSize, currentFilter);
    } catch (error) {}
  };

  const handleCopy = async (record: any) => {
    // 验证JWT
    if (!validateJWTWithMessage('copy this event')) {
      return;
    }

    try {
      setCopyLoading((prev) => ({ ...prev, [record.id]: true }));
      const updated_by = getLocalStorage('userWallet'); // 使用当前复制用户的钱包地址
      const targetRecord = copyData.find((item) => item.id === record.id);
      const newSlug = encodeContent(generateUniqueSlug(record.slug));

      // 完全按照 AddModal 的字段结构复制，与 formattedValues 保持一致
      const formattedValues = {
        // clearFormData 处理后保留的字段（除了被删除的表单特定字段）
        order_min_size: targetRecord.order_min_size,
        order_price_min_tick_size: targetRecord.order_price_min_tick_size,

        // AddModal 中明确定义的字段
        neg_risk_market_id: '', // 重置为空，等待新的市场ID
        updated_by, // 使用当前用户的钱包地址
        start_date: targetRecord.start_date,
        end_date: targetRecord.end_date,
        slug: newSlug, // 生成新的唯一slug
        title: targetRecord.title,
        description: targetRecord.description,
        active: true, // 新复制的事件默认为激活状态
        closed: false, // 新复制的事件默认为未关闭状态
        rules: targetRecord.rules,
        icon: targetRecord.icon,
        image: targetRecord.image,
        multimedia_url: targetRecord.multimedia_url,
      };

      const res = await fetchGetEventTags(record.id);
      await handleAddEvent(formattedValues, [res.data.event_tags[0].tag_id]);
      message.success('Event copied successfully');
      fetchData(undefined, pagination.current, pagination.pageSize, currentFilter);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.error || error.response?.data?.message || error.message;
        message.error(`Copied failed: ${errorMessage}`);
      }
    } finally {
      setCopyLoading((prev) => ({ ...prev, [record.id]: false }));
    }
  };

  const columns: ColumnsType<EventDataType> = [
    ...(userRole === 'admin'
      ? [
          {
            title: 'Select',
            key: 'select',
            width: 60,
            render: (_: any, record: EventDataType) => (
              <Checkbox
                checked={selectedEventId === record.id}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedEventId(record.id);
                  } else {
                    setSelectedEventId(null);
                  }
                }}
              />
            ),
          },
        ]
      : []),
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      fixed: 'left',
      render: (text) => {
        let content = '';
        if (Array.isArray(text) && text.length > 0) {
          content =
            typeof text[0] === 'object' && text[0].content ? text[0].content : String(text[0]);
        } else if (typeof text === 'object' && text !== null) {
          content = text.content || JSON.stringify(text);
        } else {
          content = String(text || '');
        }

        return (
          <Tooltip title={content}>
            <Typography.Text strong>
              <div
                style={{
                  maxWidth: 230,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {content}
              </div>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      fixed: 'left',
      render: (icon) => (
        <Image
          src={icon}
          alt="icon"
          width={40}
          height={40}
          style={{ objectFit: 'cover', objectPosition: 'center' }}
        />
      ),
    },
    {
      title: 'Active',
      dataIndex: 'active',
      key: 'active',
      width: 90,
      render: (active) => {
        const status = active ? 'yes' : 'no';
        const text = active ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    {
      title: 'Closed',
      dataIndex: 'closed',
      key: 'closed',
      width: 90,
      render: (closed) => {
        const status = closed ? 'yes' : 'no';
        const text = closed ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    {
      title: 'Updated Date(UTC)',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.updated_at ? new Date(a.updated_at).getTime() : 0;
        const dateB = b.updated_at ? new Date(b.updated_at).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Start Date(UTC)',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.start_date ? new Date(a.start_date).getTime() : 0;
        const dateB = b.start_date ? new Date(b.start_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'End Date(UTC)',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.end_date ? new Date(a.end_date).getTime() : 0;
        const dateB = b.end_date ? new Date(b.end_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 250,
      fixed: 'right',
      render: (_, record) => {
        const isPublished = record.neg_risk_market_id.length >= 5;

        // More 下拉菜单项
        const moreMenuItems: MenuProps['items'] = [
          {
            key: 'copy',
            label: 'Copy',
            onClick: () => handleCopy(record),
          },
          ...(!isPublished
            ? [
                {
                  key: 'delete',
                  label: 'Delete',
                  danger: true,
                  onClick: () => {
                    setEventId(record.id);
                    setWarningModalType('delete');
                    setIsWarningModalVisible(true);
                  },
                },
              ]
            : []),
        ];

        return (
          <div style={{ display: 'flex', gap: '4px', alignItems: 'center', whiteSpace: 'nowrap' }}>
            <Button
              onClick={() => {
                setCurrentRecord(record);
                setIsDetailModalVisible(true);
              }}
            >
              Edit
            </Button>

            {!isPublished ? (
              <Button
                type="primary"
                key={record.id}
                loading={loading[record.id] || false}
                icon={<PoweroffOutlined />}
                onClick={() => {
                  setCurrentRecord(record);
                  setWarningModalType('publish');
                  setIsWarningModalVisible(true);
                }}
              >
                Publish
              </Button>
            ) : (
              <Button
                type="primary"
                key={record.id}
                onClick={() => handleAddQuestionClick(record.id)}
              >
                Add Question
              </Button>
            )}

            <Dropdown
              menu={{
                items: moreMenuItems,
                onClick: (info) => {
                  // 处理 Copy 的 loading 状态
                  if (info.key === 'copy') {
                    // loading 状态已经在 handleCopy 函数中处理
                  }
                },
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button icon={<MoreOutlined />} loading={copyLoading[record.id] || false} />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ overflowX: 'hidden' }}>
      <Breadcrumb url="/#/events" text={'Event'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card title="Events Table">
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '24px',
              }}
            >
              <Space>
                <Button type="primary" onClick={handleAddEventClick}>
                  Add Event
                </Button>

                {userRole === 'admin' && (
                  <Button
                    type="default"
                    onClick={handleCreateBanner}
                    disabled={!selectedEventId}
                    style={{
                      backgroundColor: selectedEventId ? '#FFA726' : undefined,
                      color: selectedEventId ? 'white' : 'black',
                    }}
                  >
                    Create Banner
                  </Button>
                )}

                <Select defaultValue="all" style={{ width: 200 }} onChange={handleFilterChange}>
                  <Select.Option value="all">All</Select.Option>
                  <Select.Option value="active">Active</Select.Option>
                  <Select.Option value="closed">Closed</Select.Option>
                </Select>
              </Space>

              {/* 事件数量显示（仅对event_writer显示） */}
              {userRole === 'event_writer' && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    padding: '8px 16px',
                    background: '#f6f8fa',
                    borderRadius: '6px',
                    border: '1px solid #e1e4e8',
                  }}
                >
                  <div style={{ fontSize: '14px', color: '#586069' }}>📊 Events:</div>
                  <div style={{ display: 'flex', gap: '16px' }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        fontSize: '14px',
                      }}
                    >
                      <span style={{ color: '#28a745', fontWeight: '600' }}>
                        {eventCount.created}
                      </span>
                      <span style={{ color: '#586069' }}>created</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        fontSize: '14px',
                      }}
                    >
                      <span
                        style={{
                          color: eventCount.remaining > 0 ? '#0366d6' : '#d73a49',
                          fontWeight: '600',
                        }}
                      >
                        {eventCount.remaining}
                      </span>
                      <span style={{ color: '#586069' }}>remaining</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="table-responsive">
              <Table
                columns={columns}
                dataSource={filteredData}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                  pageSizeOptions: ['10', '20', '50', '100'],
                }}
                onChange={handleTableChange}
                className="ant-border-space"
                scroll={{ x: 1200 }}
              />
            </div>
          </Card>
        </Col>
      </Row>
      <AddModal
        visible={isModalVisible}
        setVisible={setIsModalVisible}
        onSubmit={handleAddEvent}
        fetchData={fetchData}
        onCancel={() => setIsModalVisible(false)}
      />
      <DetailModal
        visible={isDetailModalVisible}
        record={currentRecord}
        onCancel={() => {
          setIsDetailModalVisible(false);
          // 确保在Modal关闭时重新应用筛选条件
          fetchData(undefined, pagination.current, pagination.pageSize, currentFilter);
        }}
        onEdit={handleEditEvent}
      />
      <WarningModal
        visible={isWarningModalVisible}
        onClose={() => setIsWarningModalVisible(false)}
        eventId={eventId}
        type={warningModalType}
        fetchData={fetchData}
        currentRecord={currentRecord}
        setLoading={setLoading}
      />

      {/* Banner创建Modal */}
      <CreateBannerModal
        visible={isBannerModalVisible}
        onCancel={handleBannerModalCancel}
        selectedEvent={selectedEventId ? data.find((event) => event.id === selectedEventId) : null}
      />
    </div>
  );
};

export default ManageEvents;
