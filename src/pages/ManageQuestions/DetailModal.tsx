import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import {
  Modal,
  Form,
  Input,
  message,
  DatePicker,
  Row,
  Col,
  Divider,
  Select,
  Tooltip,
  InputNumber,
} from 'antd';
import ImageUpload from '@/components/ImageUpload';
import {
  formatRangePickerDates,
  decodeFormData,
  clearFormData,
  LANGUAGE_OPTIONS,
  calculateImageUrl,
} from '@/utils';

const { RangePicker } = DatePicker;

interface DetailModalProps {
  visible: boolean;
  record: any;
  onCancel: () => void;
  onEdit: (questionParams: any) => Promise<void>;
}

const DetailModal: React.FC<DetailModalProps> = ({ visible, record, onCancel, onEdit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [languages, setLanguages] = useState<string[]>(['en']);
  useEffect(() => {
    if (record) {
      const languages = record.question.map((item: { language: string }) => item.language);
      setLanguages(languages);

      const slug = record.slug.find(
        (item: { language: string }) => item.language === 'en'
      )?.content;
      const slugWithoutTimestamp = slug ? slug.replace(/_\d+$/, '') : '';
      const order_min_size = record.order_min_size ? record.order_min_size / 10 ** 6 : 1;
      const order_price_min_tick_size = record.order_price_min_tick_size
        ? record.order_price_min_tick_size - 2
        : 0;

      const fieldsValue: any = {
        ...record,
        date_range: [dayjs.utc(record.start_date), dayjs.utc(record.end_date)],
        slug: slugWithoutTimestamp,
        image: record.image,
        icon: record.icon,
        active: record.active,
        closed: record.closed,
        order_min_size,
        order_price_min_tick_size,
      };

      languages.forEach((language: string) => {
        fieldsValue[`slug_${language}`] =
          record.slug.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`title_${language}`] =
          record.question.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`description_${language}`] =
          record.description.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
      });

      form.setFieldsValue(fieldsValue);
    }
  }, [record]);

  const handleOk = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const { start_date, end_date } = formatRangePickerDates(values.date_range);
      const { slug, title, description } = decodeFormData(values);
      const { order_min_size, order_price_min_tick_size } = values;
      const clearValues = clearFormData(values);
      const order_min_size_num = Number(order_min_size) * 10 ** 6;
      const order_price_min_tick_size_num = Number(order_price_min_tick_size) + 2; // 2 is no decimal

      const formattedValues = {
        ...clearValues,
        start_date,
        end_date,
        slug,
        question: title,
        description,
        order_min_size: order_min_size_num,
        order_price_min_tick_size: order_price_min_tick_size_num,
        icon:
          typeof clearValues.icon === 'string'
            ? clearValues.icon
            : calculateImageUrl(clearValues, 'icon'),
        image:
          typeof clearValues.image === 'string'
            ? clearValues.image
            : calculateImageUrl(clearValues, 'image'),
      };

      await onEdit(formattedValues);
      message.success('Question updated successfully');
      setLoading(false);
      onCancel();
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <Modal
      width={800}
      title="Edit Question"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      {record && (
        <Form
          form={form}
          layout="inline"
          className="space-y-4"
          initialValues={{
            date_range: [dayjs.utc(record?.start_date), dayjs.utc(record?.end_date)],
            order_min_size: record?.order_min_size || 1,
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <div
                style={{
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <div style={{ height: '32px' }}>Language: </div>
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%', marginBottom: 16, marginLeft: 16 }}
                  placeholder="Please select Languages"
                  value={languages}
                  options={LANGUAGE_OPTIONS}
                  disabled
                />
              </div>

              <Divider dashed style={{ margin: '12px 0' }} />

              <Form.Item
                name="slug"
                label="Slug"
                rules={[
                  { required: true, message: 'Slug is required' },
                  {
                    pattern: /^[a-zA-Z0-9_]+$/,
                    message: 'Only letters, numbers, and underscores are allowed',
                  },
                ]}
              >
                <Input maxLength={30} disabled />
              </Form.Item>

              <Divider dashed style={{ margin: '8px 0' }} />

              {record.question.map((item: { language: string; content: string }, index: number) => (
                <div
                  key={item.language + '_' + index}
                  style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
                >
                  <Form.Item label={`Title (${item.language})`} name={`title_${item.language}`}>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    label={`Description (${item.language})`}
                    name={`description_${item.language}`}
                  >
                    <Input />
                  </Form.Item>
                  <Divider dashed style={{ margin: '8px 0' }} />
                </div>
              ))}
            </Col>
            <Col
              span={12}
              style={{
                borderLeft: '1px solid #f0f0f0',
                display: 'flex',
                flexDirection: 'column',
                gap: 4,
              }}
            >
              <Row>
                <Col span={12}>
                  <Form.Item
                    name="icon"
                    label="Icon"
                    valuePropName="fileList"
                    rules={[{ required: true }]}
                    labelCol={{ span: 8 }}
                  >
                    <ImageUpload prefix="question" maxSizeKB={64} value={record.icon} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="image"
                    label="Image"
                    valuePropName="fileList"
                    rules={[{ required: true }]}
                    labelCol={{ span: 10 }}
                  >
                    <ImageUpload prefix="question" maxSizeKB={64} value={record.image} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider dashed style={{ margin: '12px 0' }} />

              <Form.Item
                name="order_min_size"
                label={<Tooltip title="Minimum order amount">Min amount</Tooltip>}
                rules={[{ required: true, message: 'Order Min Size is required' }]}
              >
                <InputNumber<number>
                  name="order_min_size"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value?.replace(/\$\s?|(,*)/g, '') as unknown as number}
                />
              </Form.Item>
              <Form.Item
                name="order_price_min_tick_size"
                label={<Tooltip title="Integer, Limit amount decimal place">Min tick size</Tooltip>}
                rules={[{ required: true, message: 'Order Price Min Tick Size is required' }]}
              >
                <InputNumber value={0} />
              </Form.Item>

              <Form.Item
                name="date_range"
                label="Date:"
                rules={[{ required: true, message: 'Please select a date range' }]}
                layout="vertical"
              >
                <RangePicker
                  className="w-full p-2 border border-gray-300 rounded"
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
    </Modal>
  );
};

export default DetailModal;
