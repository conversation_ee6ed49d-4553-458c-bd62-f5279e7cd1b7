import React, { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';

import {
  Modal,
  Form,
  Input,
  message,
  DatePicker,
  Row,
  Col,
  Select,
  Divider,
  Tooltip,
  InputNumber,
  Upload,
  Button,
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import ImageUpload from '@/components/ImageUpload';
import { fetchQuestionList, fetchLinkEventQuestion } from '@/api/questions';
import StepFormModal from './StepFormModal';
import {
  formatRangePickerDates,
  decodeFormData,
  clearFormData,
  getLocalStorage,
  decodeQuestionData,
  calculateImageUrl,
  decode,
  LANGUAGE_OPTIONS,
  getCookie,
} from '@/utils';
const { RangePicker } = DatePicker;

interface EventModalProps {
  eventId: number;
  visible: boolean;
  currentEvent: any;
  setData: (data: any) => void;
  setVisibel: (visible: boolean) => void;
  onSubmit: (values: any) => Promise<void>;
  onCancel: () => void;
}

const AddModal: React.FC<EventModalProps> = ({
  eventId,
  visible,
  currentEvent,
  setData,
  setVisibel,
  onSubmit,
  onCancel,
}) => {
  const userRole = getCookie('jwt-role');

  // 如果是event_writer角色，使用步骤式表单
  if (userRole === 'event_writer') {
    return (
      <StepFormModal
        eventId={eventId}
        visible={visible}
        currentEvent={currentEvent}
        setData={setData}
        setVisibel={setVisibel}
        onSubmit={onSubmit}
        onCancel={onCancel}
      />
    );
  }

  // 以下是admin角色的原有表单逻辑
  const [form] = Form.useForm();
  const [languages, setLanguages] = useState<string[]>(['en']);
  const decodeTitle = currentEvent && decode(currentEvent?.title);
  const [loading, setLoading] = useState<boolean>(false);
  const processLanguages = (languages: any) => {
    const arrLanguages = languages.filter((lang: any) => lang.language);
    return [...arrLanguages.map((lang: any) => lang.language)];
  };
  const [copyedImage, setCopyedImage] = useState<any>(null);
  const [copyedIcon, setCopyedIcon] = useState<any>(null);

  useEffect(() => {
    if (decodeTitle) {
      const matchedLanguages = processLanguages(decodeTitle);
      setLanguages(matchedLanguages);
    }
  }, [currentEvent?.title]);

  const handleOk = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const { start_date, end_date } = formatRangePickerDates(values.date_range);
      const { slug, title, description } = decodeFormData(values);
      const { order_min_size, order_price_min_tick_size } = values;

      const submitted_by = getLocalStorage('userWallet');
      const timeStamps = dayjs.utc().format('YYYY-MM-DDTHH:mm:ss.SSS+00:00').toString();
      const clearValues = clearFormData(values);

      const order_min_size_num = Number(order_min_size) * 10 ** 6;
      const order_price_min_tick_size_num = Number(order_price_min_tick_size) + 2; // 2 is no decimal

      const formattedValues = {
        ...clearValues,
        start_date,
        end_date,
        slug,
        question: title,
        description,
        submitted_by,
        condition_id: 'PLACEHOLDER:' + timeStamps,
        created_at: timeStamps,
        updated_at: timeStamps,

        order_min_size: order_min_size_num,
        order_price_min_tick_size: order_price_min_tick_size_num,
        active: true,
        closed: false,

        icon:
          typeof clearValues.icon === 'string'
            ? clearValues.icon
            : calculateImageUrl(clearValues, 'icon'),
        image:
          typeof clearValues.image === 'string'
            ? clearValues.image
            : calculateImageUrl(clearValues, 'image'),
      };

      await onSubmit(formattedValues);
      await fetchLinkEventQuestion({
        event_id: eventId,
        condition_id: 'PLACEHOLDER:' + timeStamps,
      });
      const res = await fetchQuestionList(Number(eventId), submitted_by);
      setData(decodeQuestionData(res.data.events[0].event_markets));

      setLoading(false);
      message.success('Question Added Successfully');
      setVisibel(false);
      form.resetFields();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.error || error.response?.data?.message || error.message;
        setLoading(false);
        message.error(`add failed: ${errorMessage}`);
      }
    }
  };

  const handleImport = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);

        // 解码 question 和 description
        const decodedQuestion = decode(data.question); // 解码 Base64
        const decodedDescription = decode(data.description); // 解码 Base64

        // 提取语言列表
        const languages = decodedQuestion.map((item: { language: string }) => item.language);
        setLanguages(languages);

        // 处理 slug，去掉时间戳
        const slug = decode(data.slug).find(
          (item: { language: string }) => item.language === 'en'
        )?.content;
        const slugWithoutTimestamp = slug ? slug.replace(/_\d+$/, '') : '';

        // 处理 order_min_size 和 order_price_min_tick_size
        const order_min_size = data.order_min_size ? data.order_min_size / 10 ** 6 : 1;
        const order_price_min_tick_size = data.order_price_min_tick_size
          ? data.order_price_min_tick_size - 2
          : 0;

        // 初始化表单字段
        const fieldsValue: any = {
          ...data,
          slug: slugWithoutTimestamp,
          active: data.active,
          closed: data.closed,
          order_min_size,
          order_price_min_tick_size,
        };
        setCopyedImage(data.image);
        setCopyedIcon(data.icon);

        // 动态处理多语言字段
        languages.forEach((language: string) => {
          fieldsValue[`slug_${language}`] =
            decode(data.slug).find(
              (item: { language: string; content: string }) => item.language === language
            )?.content || '';
          fieldsValue[`title_${language}`] =
            decodedQuestion.find(
              (item: { language: string; content: string }) => item.language === language
            )?.content || '';
          fieldsValue[`description_${language}`] =
            decodedDescription.find(
              (item: { language: string; content: string }) => item.language === language
            )?.content || '';
        });

        if (visible) {
          form.setFieldsValue(fieldsValue);
        }
        message.success('数据导入成功');
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请检查文件格式');
      }
    };
    reader.readAsText(file);
  };

  return (
    <Modal
      width={800}
      title={
        <div style={{ marginBottom: '28px' }}>
          <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
            Add Question
            <span
              style={{
                fontSize: '12px',
                color: '#1890ff',
                marginLeft: '8px',
                padding: '2px 8px',
                background: '#e6f7ff',
                borderRadius: '4px',
                fontWeight: 'normal',
              }}
            >
              Admin Mode
            </span>
          </div>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      footer={[
        <Upload
          key="import"
          accept=".json"
          showUploadList={false}
          beforeUpload={(file) => {
            handleImport(file);
            return false;
          }}
        >
          <Button icon={<UploadOutlined />}>Import</Button>
        </Upload>,
        <Button key="cancel" onClick={onCancel} style={{ marginLeft: '8px' }}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk} loading={loading}>
          OK
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="inline"
        className="space-y-4"
        initialValues={{
          date_range: [dayjs.utc(currentEvent?.start_date), dayjs.utc(currentEvent?.end_date)],
        }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <div
              style={{
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <div style={{ height: '32px' }}>Language: </div>
              <Select
                mode="multiple"
                allowClear
                style={{ width: '100%', marginBottom: 16, marginLeft: 16 }}
                placeholder="Please select Languages"
                value={languages}
                options={LANGUAGE_OPTIONS}
                disabled
              />
            </div>

            <Divider dashed style={{ margin: '12px 0' }} />

            <Form.Item
              name="slug"
              label="Slug"
              rules={[
                { required: true, message: 'Slug is required' },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: 'Only letters, numbers, and underscores are allowed',
                },
              ]}
            >
              <Input />
            </Form.Item>

            {languages.map((language, index) => (
              <div
                key={language + '_' + index}
                style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
              >
                <Divider dashed style={{ margin: '8px 0' }} />
                <Form.Item
                  name={`title_${language}`}
                  label={`Title (${language})`}
                  rules={[{ required: true, message: 'Title is required' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name={`description_${language}`}
                  label={`Description (${language})`}
                  rules={[{ required: true, message: 'Description is required' }]}
                >
                  <Input />
                </Form.Item>
              </div>
            ))}
          </Col>
          <Col
            span={12}
            style={{
              borderLeft: '1px solid #f0f0f0',
              display: 'flex',
              flexDirection: 'column',
              gap: 4,
            }}
          >
            <Row>
              <Col span={12}>
                <Form.Item
                  name="icon"
                  label="Icon"
                  valuePropName="fileList"
                  rules={[{ required: true }]}
                  labelCol={{ span: 8 }}
                >
                  <ImageUpload prefix="event" maxSizeKB={64} value={copyedIcon} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="image"
                  label="Image"
                  valuePropName="fileList"
                  rules={[{ required: true }]}
                  labelCol={{ span: 10 }}
                >
                  <ImageUpload prefix="event" maxSizeKB={64} value={copyedImage} />
                </Form.Item>
              </Col>
            </Row>

            <Divider dashed style={{ margin: '12px 0' }} />

            <Form.Item
              name="order_min_size"
              initialValue={1}
              label={<Tooltip title="Minimum order amount">Min amount</Tooltip>}
              rules={[{ required: true, message: 'Order Min Size is required' }]}
            >
              <InputNumber<number>
                name="order_min_size"
                value={1}
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value?.replace(/\$\s?|(,*)/g, '') as unknown as number}
              />
            </Form.Item>
            <Form.Item
              initialValue={0}
              name="order_price_min_tick_size"
              label={<Tooltip title="Integer, Limit amount decimal place">Min tick size</Tooltip>}
              rules={[{ required: true, message: 'Order Price Min Tick Size is required' }]}
            >
              <InputNumber value={0} />
            </Form.Item>

            <Form.Item
              name="date_range"
              label="Date:"
              rules={[{ required: true, message: 'please select date range' }]}
              layout="vertical"
            >
              <RangePicker
                className="w-full p-2 border border-gray-300 rounded"
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                placeholder={['Start time', 'End time']}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddModal;
